// 文档拆分
import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  Tabs,
  Card,
  Flex,
  message,
  theme,
  Modal,
  Input,
} from "antd";
import type { TabsProps } from "antd";
import {
  useState,
  useEffect,
  forwardRef,
  useImperativeHandle,
  useMemo,
  useRef,
} from "react";
import { splitFile } from "@/api/public";
import "./index.less";
import DocxPreview, { DocxPreviewRef } from "@/component/DocxViewer";
import { debounce } from "@/utils/debounce";
import { NoData } from "@/component/NoData";
import { ReadOutlined, SearchOutlined } from "@ant-design/icons";

interface MentionsComponentProps {
  setGlobalLoading?: (loading: boolean) => void;
  onCallParent: (type: string, data?: any) => void; // 调用输入输出评审
  unitOutPutData?: any; // 父组件传过来的数据，用于回显跟判断有无输入输出通过
  unitInputData: {
    originalFile: any; // 原始文件
    fileList: any[]; // 文件信息列表
  };
}
export interface MentionsComponentRef {
  triggerSplit: (data: any) => void;
}
const { useToken } = theme;
const SplitPreviewModule = forwardRef<
  MentionsComponentRef,
  MentionsComponentProps
>(
  (
    {
      unitInputData = {
        originalFile: null,
        fileList: [],
      },
      setGlobalLoading,
      onCallParent,
      unitOutPutData,
    },
    ref
  ) => {
    const { token } = useToken();
    const [currentActiveKey, setCurrentActiveKey] = useState("0");
    const [chunksData, setChunksData] = useState([]); // 切分后的数据
    const [currentExtractModal, setCurrentExtractModal] = useState("1"); // 当前切分预览
    const [keyword, setKeyword] = useState("");
    const [highlightWords, setHighlightWords] = useState<string[]>([]);
    const [filteredData, setFilteredData] = useState([]);
    const [previewModal, setPreviewModal] = useState<{
      open: boolean;
      title: string;
      content: string;
    }>({ open: false, title: "", content: "" });
    const docxPreviewRef = useRef<DocxPreviewRef>(null);

    // 添加文件切割方法
    const splitDataFiles = async (files: any, originalFile: any) => {
      if (!files[0].id || !originalFile) {
        message.error("没有可切割的模板文件");
        return [];
      }
      setGlobalLoading?.(true);
      try {
        const chunks: any = [];

        // 切割模板文件
        console.log("模板文件信息:", files);

        try {
          // 使用subwayReport.ts中的splitFile方法
          const fileChunks = await splitFile(originalFile);
          // 过滤掉content为空的chunk
          const filteredChunks = fileChunks?.chunkInfo?.filter(
            (chunk: any) => chunk.text && chunk.text.trim() !== ""
          );
          if (filteredChunks.length > 0) {
            chunks.push(...filteredChunks);
          } else {
            // message.warning(`文件 ${uploadedFile.name} 切割未完成，使用原始文件`)
          }
        } catch (splitError: any) {
          console.error(`切割文件 ${files[0].name} 失败:`, splitError);
          // message.warning(`文件 ${uploadedFile.name} 切割失败: ${splitError.message || '未知错误'}，将使用原始文件`)
        }
        setGlobalLoading?.(false);

        return chunks;
      } catch (error: any) {
        console.error("文件处理失败:", error);
        // message.error(`文件处理失败: ${error.message || '未知错误'}，将使用原始文件`)
        return [];
      }
    };

    useEffect(() => {
      if (unitOutPutData?.chunksData && unitOutPutData?.chunksData.length > 0) {
        setChunksData(unitOutPutData?.chunksData);
        setFilteredData(unitOutPutData?.chunksData);
      } else {
        getChunks();
      }
    }, []);

    // 定义防抖搜索函数
    const debouncedSearch = useMemo(
      () =>
        debounce((val: string) => {
          if (!val) {
            setFilteredData(chunksData);
          } else {
            setFilteredData(
              chunksData.filter((item: any) => item?.title.includes(val))
            );
          }
        }, 500),
      [chunksData]
    ); // input 的 change 事件
    const onChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const val = e.target.value;
      setKeyword(val);
      debouncedSearch(val);
    };
    // 拿到切分的数据
    const getChunks = () => {
      splitDataFiles(unitInputData.fileList, unitInputData.originalFile)
        .then((chunks) => {
          setFilteredData(chunks);
          setChunksData(chunks);
          if (!unitOutPutData?.outputReview) {
            onCallParent?.("输出", chunks);
          }
        })
        .catch((error) => {
          console.error("文件处理失败:", error);
        });
    };
    const tabItems: TabsProps["items"] = [
      {
        key: "1",
        label: "切分预览",
      },
    ];

    // 高亮并滚动到指定位置
    const highlight = (item: any) => {
      if (!item?.title) return;

      // 使用 DocxPreview 的 highlightAndScroll 方法
      if (docxPreviewRef.current) {
        docxPreviewRef.current.highlightAndScroll(item.title);
      }

      // 同时更新 highlightWords 状态以保持一致性
      setHighlightWords((prevWords) => {
        if (prevWords.includes(item.title)) {
          return prevWords;
        }
        return [...prevWords, item.title];
      });
    };

    useImperativeHandle(ref, () => ({
      triggerSplit: async () => {
        return {
          chunksData: chunksData,
        };
      },
      splitDataFiles: () => {
        console.log("到第一步了");
        getChunks();
      },
    }));
    return (
      <div style={{ height: "100%" }} className="split-preview">
        <Row style={{ height: "100%" }}>
          <Col md={12}>
            <Tabs
              defaultActiveKey="0"
              activeKey={currentActiveKey}
              onChange={setCurrentActiveKey}
              className="split-file-list"
              items={unitInputData?.fileList?.map((x, index) => ({
                key: index + "",
                label: x.name,
                children: (
                  <div
                    style={{
                      minHeight: "calc(100vh - 215px)",
                      overflowY: "auto",
                    }}
                  >
                    <DocxPreview
                      ref={docxPreviewRef}
                      file={unitInputData?.originalFile}
                      className="my-split-docx-preview"
                      style={{ minHeight: "calc(-215px + 100vh)" }}
                      highlightWords={highlightWords}
                      onHighlightScroll={(word) => {
                        console.log(`滚动到高亮位置: ${word}`);
                      }}
                    />
                  </div>
                ),
              }))}
            />
          </Col>
          <Col xs={24} md={12} className="right">
            <Flex justify="space-between" align="center">
              <Tabs
                defaultActiveKey="1"
                items={tabItems}
                className="split-file-view"
                onChange={(e) => {
                  setCurrentExtractModal(e);
                }}
                activeKey={currentExtractModal}
              />
              <Input
                placeholder="请输入搜索的标题"
                style={{
                  width: "200px",
                  alignSelf: "flex-start",
                  marginTop: token.marginSM,
                }}
                size="large"
                onChange={onChange}
                value={keyword}
                prefix={
                  <SearchOutlined style={{ color: token.colorTextTertiary }} />
                }
                allowClear
              />
            </Flex>

            {currentExtractModal == "1" && (
              <Row
                gutter={[16, 16]}
                style={{
                  height: "calc(100vh - 213px)",
                  overflowY: "auto",
                  paddingTop: "8px",
                  paddingBottom: "20px",
                }}
              >
                {filteredData && filteredData.length > 0 ? (
                  filteredData.map((x: any, index) => (
                    <Col span={12} key={index}>
                      <Card
                        title={x.title}
                        className="split-card"
                        onClick={() => {
                          highlight(x);
                        }}
                        extra={
                          <Button
                            type="link"
                            className="split-card-detail"
                            icon={<ReadOutlined />}
                            onClick={(e) => {
                              e.stopPropagation();
                              setPreviewModal({
                                open: true,
                                title: x.title,
                                content: x.text,
                              });
                            }}
                          >
                            查看详情
                          </Button>
                        }
                        style={{ width: "100%" }}
                      >
                        <div
                          style={{
                            display: "-webkit-box",
                            WebkitLineClamp: 5,
                            WebkitBoxOrient: "vertical",
                            overflow: "hidden",
                            textOverflow: "ellipsis",
                            whiteSpace: "normal",
                            minHeight: "110px",
                            lineHeight: token.lineHeight,
                          }}
                        >
                          {x.text}
                        </div>
                      </Card>
                    </Col>
                  ))
                ) : (
                  <NoData />
                )}
              </Row>
            )}
          </Col>
        </Row>
        <Modal
          open={previewModal.open}
          title={previewModal.title}
          footer={null}
          onCancel={() =>
            setPreviewModal({ open: false, title: "", content: "" })
          }
          bodyStyle={{
            maxHeight: "70vh",
            minHeight: "500px",
            overflowY: "auto",
          }}
          style={{ top: "15vh", width: "60vw" }}
          width="70vw"
        >
          <div style={{ whiteSpace: "pre-wrap" }}>
            {(previewModal.content || "").replace(/\n{3,}/g, "\n\n")}
          </div>
        </Modal>
      </div>
    );
  }
);
// 添加 displayName
SplitPreviewModule.displayName = "SplitPreviewModule";
export default SplitPreviewModule;
